package templates


import (
    "github.com/information-sharing-networks/signalsd/app/internal/ui/client"
)

// CreateServiceAccount renders the create service account page
templ CreateServiceAccount() {
	@BaseLayout("Create New Service Account") {
		@Navigation()
		<div class="page-container">
			<h1 class="page-title">Create New Servce Account</h1>
			<div class="card mb-6">
				<div class="card">
					<div class="card-body">
						<h3 class="card-title">Debug ServiceAccounts</h3>
						<a hx-post="/ui-api/service-accounts" class="btn btn-secondary">
							Search Signals
						</a>
					</div>
				</div>
				<div class="card mb-6">     
					<div class="card-body"> 
						<form hx-post="/ui-api/create-isn" hx-target="#isn-result" class="space-y-4">
							<div class="form-group">
								<label for="title" class="form-label">Title</label>
								<input
									id="title"
									name="title"
									type="text"
									required
									class="form-input"
									placeholder="e.g., Sample ISN @example.org"
								/>
								<p class="text-muted text-sm mt-1">Unique title for the ISN. This will be used to create a URL-friendly slug.</p>
							</div>
						</form>
					</div>
				</div>
				<div class="card-body">
					<form hx-post="/ui-api/create-service-account" hx-target="#service-account-result" class="space-y-4">
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div class="form-group">
								<label for="email" class="form-label">contact email</label>
                                <input 
                                    id="email"
                                    name="email"
                                    type="email"
                                    class="form-input"
                                    placeholder="contact email for the service account"
                                />
                            </div>
						</div>
						<div class="form-group">
							<label for="organization" class="form-label">client organisation</label>
							<input
								id="organization"
								name="organization"
								type="text"
								required
								class="form-input"
								placeholder="Client organization name"
							/>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary">
								Create service account
							</button>
						</div>
					</form>
				</div>
			</div>
			<div id="service-account-result"></div>
		</div>
	}
}


templ ServiceAccountCreationSuccess(response client.CreateServiceAccountResponse) {
	<div class="card">
		<div class="card-body">
			@SuccessAlert("Service Account created successfully!")
			<div class="mt-4 space-y-2">
				<p><strong>Client ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.ClientID }</code></p>
				<p><strong>Account ID:</strong> <code class="text-sm bg-gray-100 px-2 py-1 rounded">{ response.AccountID.String() }</code></p>
				<p><strong>Setup URL:</strong>
					<code class="text-xs bg-gray-100 px-2 py-1 rounded block break-all">{ response.SetupURL }</code>
				</p>
			</div>
		</div>
	</div>
}

